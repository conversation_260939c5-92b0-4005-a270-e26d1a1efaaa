name: Release

on:
  push:
    tags:
    - v[0-9]+.[0-9]+.[0-9]+*

jobs:
  release:
    name: Publish to GitHub Release
    permissions:
      contents: write
    outputs:
      rc: ${{ steps.check-tag.outputs.rc }}

    strategy:
      matrix:
        include:
        - target: aarch64-unknown-linux-musl
          os: ubuntu-latest
          use-cross: true
          cargo-flags: ""
        - target: aarch64-apple-darwin
          os: macos-latest
          use-cross: true
          cargo-flags: ""
        - target: aarch64-pc-windows-msvc
          os: windows-latest
          use-cross: true
          cargo-flags: ""
        - target: x86_64-apple-darwin
          os: macos-latest
          cargo-flags: ""
        - target: x86_64-pc-windows-msvc
          os: windows-latest
          cargo-flags: ""
        - target: x86_64-unknown-linux-musl
          os: ubuntu-latest
          use-cross: true
          cargo-flags: ""
        - target: i686-unknown-linux-musl
          os: ubuntu-latest
          use-cross: true
          cargo-flags: ""
        - target: i686-pc-windows-msvc
          os: windows-latest
          use-cross: true
          cargo-flags: ""
        - target: armv7-unknown-linux-musleabihf
          os: ubuntu-latest
          use-cross: true
          cargo-flags: ""
        - target: arm-unknown-linux-musleabihf
          os: ubuntu-latest
          use-cross: true
          cargo-flags: ""

    runs-on: ${{matrix.os}}
    env:
      BUILD_CMD: cargo

    steps:
    - uses: actions/checkout@v4

    - name: Check Tag
      id: check-tag
      shell: bash
      run: |
        ver=${GITHUB_REF##*/}
        echo "version=$ver" >> $GITHUB_OUTPUT
        if [[ "$ver" =~ [0-9]+.[0-9]+.[0-9]+$ ]]; then
          echo "rc=false" >> $GITHUB_OUTPUT
        else
          echo "rc=true" >> $GITHUB_OUTPUT
        fi


    - name: Install Rust Toolchain Components
      uses: dtolnay/rust-toolchain@stable
      with:
        targets: ${{ matrix.target }}

    - name: Install cross
      if: matrix.use-cross
      uses: taiki-e/install-action@v2
      with:
        tool: cross

    - name: Overwrite build command env variable
      if: matrix.use-cross
      shell: bash
      run: echo "BUILD_CMD=cross" >> $GITHUB_ENV
  
    - name: Show Version Information (Rust, cargo, GCC)
      shell: bash
      run: |
        gcc --version || true
        rustup -V
        rustup toolchain list
        rustup default
        cargo -V
        rustc -V
      
    - name: Build
      shell: bash
      run: $BUILD_CMD build --locked --release --target=${{ matrix.target }} ${{ matrix.cargo-flags }}

    - name: Build Archive
      shell: bash
      id: package
      env:
        target: ${{ matrix.target }}
        version:  ${{ steps.check-tag.outputs.version }}
      run: |
        set -euxo pipefail

        bin=${GITHUB_REPOSITORY##*/}
        dist_dir=`pwd`/dist
        name=$bin-$version-$target
        executable=target/$target/release/$bin

        if [[ "$RUNNER_OS" == "Windows" ]]; then
          executable=$executable.exe
        fi

        mkdir $dist_dir
        cp $executable $dist_dir
        cd $dist_dir

        if [[ "$RUNNER_OS" == "Windows" ]]; then
            archive=$dist_dir/$name.zip
            7z a $archive *
            echo "archive=dist/$name.zip" >> $GITHUB_OUTPUT
        else
            archive=$dist_dir/$name.tar.gz
            tar -czf $archive *
            echo "archive=dist/$name.tar.gz" >> $GITHUB_OUTPUT
        fi

    - name: Publish Archive
      uses: softprops/action-gh-release@v2
      if: ${{ startsWith(github.ref, 'refs/tags/') }}
      with:
        draft: false
        files: ${{ steps.package.outputs.archive }}
        prerelease: ${{ steps.check-tag.outputs.rc == 'true' }}

  publish-crate:
    name: Publish to crates.io
    if: ${{ needs.release.outputs.rc == 'false' }}
    runs-on: ubuntu-latest
    needs: release
    steps:
      - uses: actions/checkout@v4

      - uses: dtolnay/rust-toolchain@stable

      - name: Publish
        env:
          CARGO_REGISTRY_TOKEN: ${{ secrets.CRATES_IO_API_TOKEN }}
        run: cargo publish