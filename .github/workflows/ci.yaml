name: CI

on:
  pull_request:
    branches:
    - '*'
  push:
    branches:
    - main

defaults:
  run:
    shell: bash

jobs:
  all:
    name: All

    strategy:
      matrix:
        os:
        - ubuntu-latest
        - macos-latest
        - windows-latest

    runs-on: ${{matrix.os}}

    env:
      RUSTFLAGS: --deny warnings

    steps:
    - uses: actions/checkout@v4

    - name: Install Rust Toolchain Components
      uses: dtolnay/rust-toolchain@stable

    - uses: Swatinem/rust-cache@v2

    - name: Test
      run: cargo test --all

    - name: Clippy
      run: cargo clippy --all --all-targets -- -D warnings

    - name: Format
      run: cargo fmt --all --check