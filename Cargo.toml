[package]
name = "aichat"
version = "0.29.0"
edition = "2021"
authors = ["sigoden <<EMAIL>>"]
description = "All-in-one LLM CLI Tool"
license = "MIT OR Apache-2.0"
homepage = "https://github.com/sigoden/aichat"
repository = "https://github.com/sigoden/aichat"
categories = ["command-line-utilities"]
keywords = ["chatgpt", "llm", "cli", "ai", "repl"]

[dependencies]
anyhow = "1.0.69"
bytes = "1.4.0"
clap = { version = "4.4.8", features = ["derive"] }
dirs = "6.0.0"
futures-util = "0.3.29"
inquire = "0.7.0"
is-terminal = "0.4.9"
reedline = "0.39.0"
serde = { version = "1.0.152", features = ["derive"] }
serde_json = { version = "1.0.93", features = ["preserve_order"] }
serde_yaml = "0.9.17"
tokio = { version = "1.34.0", features = ["rt", "time", "macros", "signal", "rt-multi-thread"] }
tokio-graceful = "0.2.2"
tokio-stream = { version = "0.1.15", default-features = false, features = ["sync"] }
crossterm = "0.28.1"
chrono = "0.4.23"
bincode = "1.3.3"
parking_lot = "0.12.1"
fancy-regex = "0.14.0"
base64 = "0.22.0"
nu-ansi-term = "0.50.0"
async-trait = "0.1.74"
textwrap = "0.16.0"
ansi_colours = "1.2.2"
reqwest-eventsource = "0.6.0"
simplelog = "0.12.1"
log = "0.4.20"
shell-words = "1.1.0"
sha2 = "0.10.8"
unicode-width = "0.2.0"
async-recursion = "1.1.1"
http = "1.1.0"
http-body-util = "0.1"
hyper = { version = "1.0", features = ["full"] }
hyper-util = { version = "0.1", features = ["server-auto", "client-legacy"] }
time = { version = "0.3.36", features = ["macros"] }
indexmap = { version = "2.2.6", features = ["serde"] }
hmac = "0.12.1"
aws-smithy-eventstream = "0.60.4"
urlencoding = "2.1.3"
unicode-segmentation = "1.11.0"
json-patch = { version = "4.0.0", default-features = false }
bitflags = "2.5.0"
path-absolutize = "3.1.1"
hnsw_rs = "0.3.0"
rayon = "1.10.0"
uuid = { version = "1.9.1", features = ["v4"] }
scraper = { version = "0.23.1", default-features = false, features = ["deterministic"] }
sys-locale = "0.3.1"
html_to_markdown = "0.1.0"
rust-embed = "8.5.0"
os_info = { version = "3.8.2", default-features = false }
bm25 = { version = "2.0.1", features = ["parallelism"] }
which = "7.0.1"
fuzzy-matcher = "0.3.7"

[dependencies.reqwest]
version = "0.12.0"
features = ["json", "multipart", "socks", "rustls-tls", "rustls-tls-native-roots"]
default-features = false

[dependencies.syntect]
version = "5.0.0"
default-features = false
features = ["parsing", "regex-onig", "plist-load"]

[target.'cfg(target_os = "macos")'.dependencies]
crossterm = { version = "0.28.1", features = ["use-dev-tty"] }

[target.'cfg(target_os = "linux")'.dependencies]
arboard = { version = "3.3.0", default-features = false, features = ["wayland-data-control"] }

[target.'cfg(not(any(target_os = "linux", target_os = "android", target_os = "emscripten")))'.dependencies]
arboard = { version = "3.3.0", default-features = false }

[dev-dependencies]
pretty_assertions = "1.4.0"
rand = "0.9.0"

[profile.release]
lto = true
strip = true
opt-level = "z"
