Set-PSReadLineKey<PERSON><PERSON><PERSON> -Chord "alt+e" -<PERSON>ript<PERSON><PERSON> {
    $_old = $null
    [Microsoft.PowerShell.PSConsoleReadline]::GetBufferState([ref]$_old, [ref]$null)
    if ($_old) {
        [Microsoft.PowerShell.PSConsoleReadLine]::Insert('⌛')
        $_new = (aichat -e $_old)
        [Microsoft.PowerShell.PSConsoleReadLine]::DeleteLine()
        [Microsoft.PowerShell.PSConsoleReadline]::Insert($_new)
    }
}