use reedline::{DefaultPrompt, DefaultPromptSegment, Reedline, Signal};
use rust_llm_tui::{app::App, display};
use std::io::Result;

fn main() -> Result<()> {
    let demo_markdown = ;
    // Create app and parse the markdown
    let app = App::new(reqwest::Client::new());
    let app_message = app.create_ai_app_message_from_raw(demo_markdown);

    // Set up Reedline
    let mut line_editor = Reedline::create();
    let prompt = DefaultPrompt::new(
        DefaultPromptSegment::Basic("Enter anything to see markdown demo > ".to_string()),
        DefaultPromptSegment::Empty,
    );

    // Main input loop
    loop {
        match line_editor.read_line(&prompt) {
            Ok(Signal::Success(input)) => {
                println!("You entered: {}", input);

                // Always display the markdown demo regardless of input
                if let Err(e) = display::print_formatted_message(&app_message) {
                    eprintln!("Error displaying message: {}", e);
                }

                // Exit if user types "exit"
                if input.trim().eq_ignore_ascii_case("exit") {
                    break;
                }
            }
            Ok(Signal::CtrlC) | Ok(Signal::CtrlD) => {
                println!("Exiting...");
                break;
            }
            Err(err) => {
                eprintln!("Error: {}", err);
                break;
            }
        }
    }

    log::info!("Reedline markdown test completed");
    Ok(())
}
