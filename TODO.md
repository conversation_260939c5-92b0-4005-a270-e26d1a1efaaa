# aichat

it uses reedline, but without external printer

add the remaining loading spinner from aichat to this repo, but use this emoji instead and flicker through the items ⣻

it has options to have a menu, how is it implemented, later for shell command options:
the repl
temp) .rag                                                                                                591(0.06%)
⚙ Initializing RAG...
? Select embedding model:
> gemini:text-embedding-004 (max-tokens:2048;max-batch:100;price:0)
[↑↓ to move, enter to select, type to filter]

# others

even with instantdummy, the initial startup is sluggish, as well as sending a message which is weird

fix broken ai message display, it works in `cargo run --example reedline_markdown_test` though
there must be a difference, what is it?
render_prompt_indicator is not the issue or its left and right fns, I tested

fix that sessions are always created in cwd instead of a hardcoded directory

if --restore is passed with empty string, use latest session based on the session directories last modified

make ai messages border a green color, use a new color in src/display/colors.rs
and also on first line display it like this
╭─ Response ──────────────────────────────────────────────────────────────────────────────╮

AI messages and tool output display is entirely broken
it is not in markdown formatted?

show menu completion when /

ensure that there is newline at bottom of rendered ai message markdown, to better differentiate ai response to prompt input

ensure flattened newlines before and after headings

using find and wc, refactor all files above 300 lines

add working in {git root dir} note on startup

why is this used on the raw response "content" JSON string? Tools are ALWAYS only considered inside the request body tools array
try_parse_tool_call(raw_content) {

this is wrong in tool_executor.rs, it should not be system message
let err_display_msg = AppMessage {
sender: MessageSender::System,
parts: vec![MessageContent::Text(err_text)],
};

needs runtime model change capabilities, but only for ones with tool support
add gemini 2.0 flash provider (use https://ai.google.dev/gemini-api/docs/openai?hl=de)
add cohere provider

# switch to async-openai crate for more correct tiktoken usage for token counting

add a test first that tools affect token count!

Counting max_tokens parameter for a chat completion request with async-openai
rewrite to use ChatCompletionRequestMessage as shown below

use tiktoken_rs::async_openai::get_chat_completion_max_tokens;
use async_openai::types::{ChatCompletionRequestMessage, Role};

let messages = vec![
ChatCompletionRequestMessage {
content: Some("You are a helpful assistant that only speaks French.".to_string()),
role: Role::System,
name: None,
function_call: None,
},
ChatCompletionRequestMessage {
content: Some("Hello, how are you?".to_string()),
role: Role::User,
name: None,
function_call: None,
},
ChatCompletionRequestMessage {
content: Some("Parlez-vous francais?".to_string()),
role: Role::System,
name: None,
function_call: None,
},
];
let max_tokens = get_chat_completion_max_tokens("o1-mini", &messages).unwrap();
println!("max_tokens: {}", max_tokens);