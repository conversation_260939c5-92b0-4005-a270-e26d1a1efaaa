use clap::{<PERSON><PERSON><PERSON>, ValueEnum};

#[derive(ValueE<PERSON>, Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum ModelType {
    /// Grok model from pollinations.ai
    PollinationsAiGrok,
    /// DeepSeek Reasoning 1 0528 from pollinations.ai
    PollinationsAiDeepseekR1,
    /// Dummy model that returns a fixed response
    InstantDummy,
    /// Dummy model that returns a fixed response after 1 second
    DelayedDummy,
}

// Required by clap for command line parsing
impl std::fmt::Display for ModelType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.to_string_value())
    }
}

impl ModelType {
    // Returns the string value for command line parsing
    pub fn to_string_value(&self) -> &'static str {
        match self {
            ModelType::PollinationsAiGrok => "grok",
            ModelType::PollinationsAiDeepseekR1 => "deepseek-reasoning",
            ModelType::InstantDummy => "dummy",
            ModelType::DelayedDummy => "dummy [1s delay]",
        }
    }

    // Returns the actual model name to use in API calls
    pub fn api_model_name(&self) -> &'static str {
        match self {
            ModelType::PollinationsAiGrok => "grok-3-mini-high",
            ModelType::PollinationsAiDeepseekR1 => "deepseek-reasoning",
            ModelType::InstantDummy => "dummy",
            ModelType::DelayedDummy => "dummy",
        }
    }

    pub fn is_instant(&self) -> bool {
        match self {
            ModelType::InstantDummy => true,
            _ => false,
        }
    }

    // Returns a descriptive text for the startup banner
    pub fn startup_banner_text(&self) -> String {
        match self {
            ModelType::PollinationsAiGrok => "🤖 Pollinations.AI Grok-3-mini-high".to_string(),
            ModelType::PollinationsAiDeepseekR1 => {
                "🧠 Pollinations.AI DeepSeek Reasoning 1 0528".to_string()
            }
            ModelType::InstantDummy => "🌀 Dummy Model".to_string(),
            ModelType::DelayedDummy => "🌀 Dummy Model [1s delay]".to_string(),
        }
    }
}

#[derive(Parser, Debug, Clone)]
#[command(author, version, about, long_about = None)]
pub struct Cli {
    #[arg(long, value_name = "PORT")]
    pub proxy_localhost: Option<u16>,

    #[arg(long, value_name = "SESSION_ID")]
    pub restore: Option<String>,

    #[arg(
        long,
        value_name = "TOKEN_LIMIT",
        help = "Token limit for workspace injection",
        default_value = "1000"
    )]
    pub workspace_token_limit: usize,

    #[arg(long, value_name = "MODEL", help = "Model to use for LLM API calls", default_value_t = ModelType::PollinationsAiGrok)]
    pub model: ModelType,
}

pub fn parse_cli_args() -> Cli {
    Cli::parse()
}
