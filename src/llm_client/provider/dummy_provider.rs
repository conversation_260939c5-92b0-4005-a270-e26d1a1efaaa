use crate::llm_client::types::LLMError;
use crate::utils::AbortSignal;
use std::time::Duration;
use tokio::time::sleep;

pub async fn call_dummy_provider(
    instant: bool,
    abort_signal: &AbortSignal,
) -> Result<String, LLMError> {
    if instant {
        return Ok(include_str!("../../../tests/markdown_features_demo.md").into());
    }
    let delay_ms = 1000;
    let delay_duration = Duration::from_millis(delay_ms);
    for _ in 0..10 {
        // Check for abort signal every 10% of the total delay
        if abort_signal.aborted() {
            return Err(LLMError::ApiError("Request cancelled by user".to_string()));
        }
        sleep(delay_duration / 10).await;
    }
    if abort_signal.aborted() {
        return Err(LLMError::ApiError("Request cancelled by user".to_string()));
    }
    Ok(include_str!("../../../tests/markdown_features_demo.md").into())
}
