use crate::llm_client::{types, ToolCallAction};

pub fn try_parse_tool_call(content: &str) -> Option<ToolCallAction> {
    let trimmed_content = content.trim();

    // First try to parse as OpenAI-style response with tool_calls array
    if let Ok(response_with_tool_calls) = serde_json::from_str::<serde_json::Value>(trimmed_content) {
        if let Some(tool_calls_array) = response_with_tool_calls.get("tool_calls").and_then(|v| v.as_array()) {
            if let Some(first_tool_call) = tool_calls_array.first() {
                if let Ok(tool_call) = serde_json::from_value::<types::ToolCall>(first_tool_call.clone()) {
                    // Parse the arguments JSON string
                    if let Ok(arguments) = serde_json::from_str::<serde_json::Value>(&tool_call.function.arguments) {
                        return Some(ToolCallAction {
                            tool_name: tool_call.function.name,
                            arguments,
                            id: Some(tool_call.id),
                        });
                    }
                }
            }
        }
    }

    // Fallback: try to parse as simple ToolCallAction format (backward compatibility)
    if trimmed_content.starts_with('{') && trimmed_content.ends_with('}') {
        if let Ok(mut tool_action) = serde_json::from_str::<ToolCallAction>(trimmed_content) {
            // Ensure id is set if not present
            if tool_action.id.is_none() {
                tool_action.id = Some(format!("call_{}", &uuid::Uuid::new_v4().to_string().replace('-', "")[..8]));
            }
            return Some(tool_action);
        }
    }

    None
}


#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_parse_openai_tool_call_format() {
        let openai_response = json!({
            "tool_calls": [
                {
                    "id": "call_18418396",
                    "type": "function",
                    "function": {
                        "name": "search_code",
                        "arguments": "{\"pattern\":\"/clear\",\"path\":\"/Users/<USER>/Developer/ai/rust-ai-agent\"}"
                    }
                }
            ]
        });

        let response_str = openai_response.to_string();
        let result = try_parse_tool_call(&response_str);

        assert!(result.is_some());
        let tool_call = result.unwrap();
        assert_eq!(tool_call.tool_name, "search_code");
        assert_eq!(tool_call.id, Some("call_18418396".to_string()));

        // Check that arguments were parsed correctly
        assert_eq!(tool_call.arguments.get("pattern").unwrap().as_str().unwrap(), "/clear");
        assert_eq!(tool_call.arguments.get("path").unwrap().as_str().unwrap(), "/Users/<USER>/Developer/ai/rust-ai-agent");
    }

    #[test]
    fn test_parse_legacy_tool_call_format() {
        let legacy_format = json!({
            "tool_name": "read_file",
            "arguments": {
                "path": "src/main.rs",
                "offset": 0
            }
        });

        let response_str = legacy_format.to_string();
        let result = try_parse_tool_call(&response_str);

        assert!(result.is_some());
        let tool_call = result.unwrap();
        assert_eq!(tool_call.tool_name, "read_file");
        assert!(tool_call.id.is_some()); // Should generate an ID

        // Check that arguments were preserved
        assert_eq!(tool_call.arguments.get("path").unwrap().as_str().unwrap(), "src/main.rs");
        assert_eq!(tool_call.arguments.get("offset").unwrap().as_i64().unwrap(), 0);
    }
}
