use crossterm::style::Color;

/// IntelliJ background color
pub const CODE_BLOCK_BACKGROUND: Color = Color::Rgb {
    r: 43,
    g: 43,
    b: 43,
};

/// Full white for difference to other text color
pub const BOLD_AND_ITALIC_BACKGROUND_COLOR: Color = Color::Rgb {
    r: 33,
    g: 33,
    b: 33,
};

/// Use same color as in Rovo Dev.
pub const INLINE_CODE_COLOR: Color = Color::Rgb {
    r: 108,
    g: 177,
    b: 205,
};

/// Orangish. Same color as in Rovo Dev.
pub const LIST_AND_RULE_CIRCLE_COLOR: Color = Color::Rgb {
    r: 231,
    g: 185,
    b: 77,
};

/// Use same color as in Rovo Dev.
pub const HEADING_H4_COLOR: Color = Color::Rgb {
    r: 150,
    g: 150,
    b: 150,
};

/// Orange color for interrupt messages, same as in Rovo Dev.
pub const ORANGE_COLOR: Color = Color::Rgb {
    r: 255,
    g: 165,
    b: 0,
};

/// Blue color for command output borders.
pub const BLUE_COLOR: Color = Color::Rgb {
    r: 97,
    g: 175,
    b: 239,
};

/// Red color.
pub const TOOL_OUTPUT_TRUNCATE_COLOR: Color = Color::Rgb {
    r: 224,
    g: 108,
    b: 117,
};

pub const INFO_BORDER_COLOR: Color = Color::Blue;
