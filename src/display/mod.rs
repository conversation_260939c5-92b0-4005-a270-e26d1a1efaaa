pub mod ai_message;
pub mod colors;
pub mod common;
pub mod rounded_message;
pub mod system_message;
pub mod user_message;

use crate::app::{AppMessage, MessageSender};
pub use common::truncate_tool_output;
use std::io;

use crate::display::colors::BLUE_COLOR;
use crossterm::style::Stylize;

pub fn print_formatted_message(app_msg: &AppMessage) -> io::Result<()> {
    match app_msg.sender {
        // TODO test
        MessageSender::User => {
            // user_message::print_user_message(app_msg)
            Ok(())
        }
        MessageSender::AI => ai_message::print_ai_message(app_msg),
        MessageSender::System | MessageSender::ToolExecution => {
            system_message::print_system_message(app_msg)
        }
    }
}

pub fn print_command_output(message: &str) -> Result<(), Box<dyn std::error::Error>> {
    let (terminal_width, _) = crossterm::terminal::size().unwrap_or((80, 24));
    let width = terminal_width as usize;

    // Create blue rounded border
    let top_border = format!("╭{}╮", "─".repeat(width.saturating_sub(2)));
    let bottom_border = format!("╰{}╯", "─".repeat(width.saturating_sub(2)));

    println!("{}", top_border.with(BLUE_COLOR));

    // Add message content with padding
    for line in message.lines() {
        let content_width = width.saturating_sub(4); // Account for borders and padding
        if line.len() > content_width {
            // Wrap long lines
            for chunk in line.chars().collect::<Vec<_>>().chunks(content_width) {
                let chunk_str: String = chunk.iter().collect();
                println!(
                    "│ {} │",
                    format!("{:<width$}", chunk_str, width = content_width).with(BLUE_COLOR)
                );
            }
        } else {
            println!(
                "│ {} │",
                format!("{:<width$}", line, width = content_width).with(BLUE_COLOR)
            );
        }
    }

    println!("{}", bottom_border.with(BLUE_COLOR));
    Ok(())
}

fn format_user_message_as_string(app_msg: &AppMessage) -> String {
    use crossterm::style::Stylize;

    // Get the text content
    let text_content = if let Some(crate::app::MessageContent::Text(text)) = app_msg.parts.first() {
        text.lines().next().unwrap_or("").trim()
    } else {
        ""
    };

    // Format the text with color and simple prompt
    let colored_text = format!("{}", text_content.cyan());

    // Simple format: "> text"
    format!("> {}", colored_text)
}

fn format_ai_message_as_string(app_msg: &AppMessage) -> String {
    // This is a simplified version that captures the essential AI message formatting
    // For the external printer, we'll use a more basic but still nicely formatted approach
    let mut result = String::new();

    use crossterm::terminal;
    let (terminal_width, _) = terminal::size().unwrap_or((80, 24));

    result.push('\n'); // Initial newline for AI message block

    // Draw top border for AI response
    let border_width = terminal_width as usize;
    let top_border = format!("╭{}╮", "─".repeat(border_width.saturating_sub(2)));
    result.push_str(&format!("{}\n", top_border));

    // Calculate content width for proper padding
    let outer_left_margin = "│ ";
    let outer_right_margin = " │";
    let content_width = terminal_width
        .saturating_sub(outer_left_margin.len() as u16 + outer_right_margin.len() as u16);

    for part in &app_msg.parts {
        match part {
            crate::app::MessageContent::Text(text_content) => {
                // Add border margins for all AI messages with proper padding
                for line in text_content.lines() {
                    let padded_line = format!("{:<width$}", line, width = content_width as usize);
                    result.push_str(&format!(
                        "{}{}{}\n",
                        outer_left_margin, padded_line, outer_right_margin
                    ));
                }
            }
            crate::app::MessageContent::FormattedText(elements) => {
                // Process formatted elements with basic formatting
                for element in elements {
                    match element {
                        crate::app::FormattedTextElement::ListStart => {
                            // Add a newline before list
                            result.push('\n');
                        }
                        crate::app::FormattedTextElement::ListEnd => {
                            // Add a newline after list
                            result.push('\n');
                        }
                        crate::app::FormattedTextElement::Text(text) => {
                            for line in text.lines() {
                                let padded_line =
                                    format!("{:<width$}", line, width = content_width as usize);
                                result.push_str(&format!(
                                    "{}{}{}\n",
                                    outer_left_margin, padded_line, outer_right_margin
                                ));
                            }
                        }
                        crate::app::FormattedTextElement::Heading { level, text } => {
                            result.push('\n');
                            match level {
                                1 => {
                                    // Special border formatting for H1 with proper padding
                                    let border_width = content_width as usize;
                                    let top_border =
                                        format!("┏{}┓", "━".repeat(border_width.saturating_sub(2)));
                                    let padded_top = format!(
                                        "{:<width$}",
                                        top_border,
                                        width = content_width as usize
                                    );
                                    result.push_str(&format!(
                                        "{}{}{}\n",
                                        outer_left_margin, padded_top, outer_right_margin
                                    ));

                                    let content_padding =
                                        (border_width.saturating_sub(text.len() + 2)) / 2;
                                    let content_line = format!(
                                        "┃{}{}{}┃",
                                        " ".repeat(content_padding),
                                        text,
                                        " ".repeat(
                                            border_width
                                                .saturating_sub(2 + content_padding + text.len())
                                        )
                                    );
                                    let padded_content = format!(
                                        "{:<width$}",
                                        content_line,
                                        width = content_width as usize
                                    );
                                    result.push_str(&format!(
                                        "{}{}{}\n",
                                        outer_left_margin, padded_content, outer_right_margin
                                    ));

                                    let bottom_border =
                                        format!("┗{}┛", "━".repeat(border_width.saturating_sub(2)));
                                    let padded_bottom = format!(
                                        "{:<width$}",
                                        bottom_border,
                                        width = content_width as usize
                                    );
                                    result.push_str(&format!(
                                        "{}{}{}\n",
                                        outer_left_margin, padded_bottom, outer_right_margin
                                    ));
                                }
                                _ => {
                                    let padded_line =
                                        format!("{:<width$}", text, width = content_width as usize);
                                    result.push_str(&format!(
                                        "{}{}{}\n",
                                        outer_left_margin, padded_line, outer_right_margin
                                    ));
                                }
                            }
                            result.push('\n');
                        }
                        crate::app::FormattedTextElement::ListItem {
                            text,
                            indent_level,
                            is_ordered,
                            number,
                            ..
                        } => {
                            let indent = "  ".repeat(*indent_level as usize + 1); // +1 for base margin
                            let bullet = if *is_ordered {
                                if let Some(num) = number {
                                    format!("{}.", num)
                                } else {
                                    "1.".to_string()
                                }
                            } else {
                                "•".to_string()
                            };
                            let list_line = format!("{}{} {}", indent, bullet, text);
                            let padded_line =
                                format!("{:<width$}", list_line, width = content_width as usize);
                            result.push_str(&format!(
                                "{}{}{}\n",
                                outer_left_margin, padded_line, outer_right_margin
                            ));
                        }
                        crate::app::FormattedTextElement::InlineCode(text) => {
                            result.push_str(&format!("`{}`", text));
                        }
                        crate::app::FormattedTextElement::BoldOrItalic(text) => {
                            result.push_str(text); // Skip bold formatting for external printer
                        }
                        crate::app::FormattedTextElement::Link { text, .. } => {
                            result.push_str(text); // Just show the text for external printer
                        }
                        crate::app::FormattedTextElement::HorizontalRuler => {
                            let ruler_line = "  ───";
                            let padded_line =
                                format!("{:<width$}", ruler_line, width = content_width as usize);
                            result.push_str(&format!(
                                "{}{}{}\n",
                                outer_left_margin, padded_line, outer_right_margin
                            ));
                        }
                        crate::app::FormattedTextElement::LineBreak => {
                            // Add an empty line with proper padding
                            let empty_line = "";
                            let padded_line =
                                format!("{:<width$}", empty_line, width = content_width as usize);
                            result.push_str(&format!(
                                "{}{}{}\n",
                                outer_left_margin, padded_line, outer_right_margin
                            ));
                        }
                        crate::app::FormattedTextElement::Strikethrough(text) => {
                            result.push_str(text); // Skip strikethrough formatting for external printer
                        }
                        crate::app::FormattedTextElement::Table { headers, rows, .. } => {
                            // Simple table representation with proper margins and padding
                            let header_line = format!("  {}", headers.join(" | "));
                            let padded_header =
                                format!("{:<width$}", header_line, width = content_width as usize);
                            result.push_str(&format!(
                                "{}{}{}\n",
                                outer_left_margin, padded_header, outer_right_margin
                            ));

                            for row in rows {
                                let row_line = format!("  {}", row.join(" | "));
                                let padded_row =
                                    format!("{:<width$}", row_line, width = content_width as usize);
                                result.push_str(&format!(
                                    "{}{}{}\n",
                                    outer_left_margin, padded_row, outer_right_margin
                                ));
                            }
                        }
                    }
                }
            }
            crate::app::MessageContent::CodeBlock { language, content } => {
                // Code block start
                let start_line = format!("  ```{}", language.as_deref().unwrap_or(""));
                let padded_start =
                    format!("{:<width$}", start_line, width = content_width as usize);
                result.push_str(&format!(
                    "{}{}{}\n",
                    outer_left_margin, padded_start, outer_right_margin
                ));

                // Code content
                for line in content.lines() {
                    let code_line = format!("  {}", line);
                    let padded_code =
                        format!("{:<width$}", code_line, width = content_width as usize);
                    result.push_str(&format!(
                        "{}{}{}\n",
                        outer_left_margin, padded_code, outer_right_margin
                    ));
                }

                // Code block end
                let end_line = "  ```";
                let padded_end = format!("{:<width$}", end_line, width = content_width as usize);
                result.push_str(&format!(
                    "{}{}{}\n",
                    outer_left_margin, padded_end, outer_right_margin
                ));
            }
            crate::app::MessageContent::ToolCall(tool_call) => {
                let tool_line = format!(
                    "  └── Calling {}: {}",
                    tool_call.tool_name,
                    truncate_tool_output(&format!("{:?}", tool_call.arguments))
                );
                let padded_tool = format!("{:<width$}", tool_line, width = content_width as usize);
                result.push_str(&format!(
                    "{}{}{}\n",
                    outer_left_margin, padded_tool, outer_right_margin
                ));
            }
        }
    }

    // Draw bottom border for AI response
    let bottom_border = format!("╰{}╯", "─".repeat(border_width.saturating_sub(2)));
    result.push_str(&format!("{}\n", bottom_border));

    result
}

/// Display a command output with blue rounded border

fn format_system_message_as_string(app_msg: &AppMessage) -> String {
    let mut result = String::new();

    let sender_prefix = format!("{}: ", app_msg.sender.as_str());

    if app_msg.parts.is_empty() {
        result.push_str(&sender_prefix);
        result.push('\n');
    } else {
        let mut first_line = true;
        for part in &app_msg.parts {
            match part {
                crate::app::MessageContent::Text(text_content) => {
                    for line in text_content.lines() {
                        if first_line {
                            result.push_str(&format!("{}{}\n", sender_prefix, line));
                            first_line = false;
                        } else {
                            let padding = " ".repeat(sender_prefix.len());
                            result.push_str(&format!("{}{}\n", padding, line));
                        }
                    }
                }
                _ => {
                    if first_line {
                        result.push_str(&format!("{}[Non-text content]\n", sender_prefix));
                        first_line = false;
                    }
                }
            }
        }
    }

    result.push('\n'); // Ensure a blank line after the message block
    result
}
